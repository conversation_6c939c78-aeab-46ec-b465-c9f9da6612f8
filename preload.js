/**
 * ShareWharf 桌面应用预加载脚本
 * 用于增强用户体验和提供额外功能
 */

const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// 在页面加载完成后执行的优化脚本
window.addEventListener('DOMContentLoaded', () => {
  console.log('ShareWharf 桌面应用已启动');
  
  // 添加自定义样式来优化显示效果
  const style = document.createElement('style');
  style.textContent = `
    /* 优化滚动条样式 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: linear-gradient(45deg, #667eea, #764ba2);
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(45deg, #5a6fd8, #6a4190);
    }
    
    /* 优化选择文本的颜色 */
    ::selection {
      background-color: rgba(102, 126, 234, 0.3);
      color: inherit;
    }
    
    /* 添加平滑滚动 */
    html {
      scroll-behavior: smooth;
    }
    
    /* 优化焦点样式 */
    *:focus {
      outline: 2px solid rgba(102, 126, 234, 0.5);
      outline-offset: 2px;
    }
    
    /* 添加加载动画 */
    .sharewharf-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2);
      z-index: 9999;
      animation: loading 2s ease-in-out infinite;
    }
    
    @keyframes loading {
      0% { transform: translateX(-100%); }
      50% { transform: translateX(0%); }
      100% { transform: translateX(100%); }
    }
  `;
  
  document.head.appendChild(style);
  
  // 添加键盘快捷键支持
  document.addEventListener('keydown', (event) => {
    // Ctrl+R 或 F5 刷新页面
    if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
      event.preventDefault();
      window.location.reload();
    }
    
    // Ctrl+Shift+R 强制刷新
    if (event.ctrlKey && event.shiftKey && event.key === 'R') {
      event.preventDefault();
      window.location.reload(true);
    }
    
    // Ctrl+0 重置缩放
    if (event.ctrlKey && event.key === '0') {
      event.preventDefault();
      document.body.style.zoom = '1';
    }
    
    // Ctrl++ 放大
    if (event.ctrlKey && (event.key === '+' || event.key === '=')) {
      event.preventDefault();
      const currentZoom = parseFloat(document.body.style.zoom || '1');
      document.body.style.zoom = Math.min(currentZoom + 0.1, 2).toString();
    }
    
    // Ctrl+- 缩小
    if (event.ctrlKey && event.key === '-') {
      event.preventDefault();
      const currentZoom = parseFloat(document.body.style.zoom || '1');
      document.body.style.zoom = Math.max(currentZoom - 0.1, 0.5).toString();
    }
  });
  
  // 添加右键菜单优化
  document.addEventListener('contextmenu', (event) => {
    // 可以在这里添加自定义右键菜单逻辑
    // 目前保持默认行为
  });
  
  // 监听页面加载状态
  let loadingBar = null;
  
  // 页面开始加载时显示加载条
  window.addEventListener('beforeunload', () => {
    if (!loadingBar) {
      loadingBar = document.createElement('div');
      loadingBar.className = 'sharewharf-loading';
      document.body.appendChild(loadingBar);
    }
  });
  
  // 页面加载完成后隐藏加载条
  window.addEventListener('load', () => {
    if (loadingBar) {
      setTimeout(() => {
        loadingBar.remove();
        loadingBar = null;
      }, 500);
    }
  });
  
  // 添加网络状态监听
  window.addEventListener('online', () => {
    console.log('网络连接已恢复');
    // 可以在这里添加网络恢复后的处理逻辑
  });
  
  window.addEventListener('offline', () => {
    console.log('网络连接已断开');
    // 可以在这里添加网络断开后的处理逻辑
  });
  
  // 优化图片加载
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    img.addEventListener('error', () => {
      // 图片加载失败时的处理
      img.style.opacity = '0.5';
      img.title = '图片加载失败';
    });
  });
  
  // 添加页面性能监控
  if (window.performance && window.performance.timing) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const timing = window.performance.timing;
        const loadTime = timing.loadEventEnd - timing.navigationStart;
        console.log(`页面加载时间: ${loadTime}ms`);
      }, 0);
    });
  }
});

// 暴露一些有用的 API 给渲染进程
contextBridge.exposeInMainWorld('sharewharf', {
  // 获取应用版本信息
  getVersion: () => {
    return '1.0.0';
  },
  
  // 获取平台信息
  getPlatform: () => {
    return process.platform;
  },
  
  // 打印调试信息
  log: (message) => {
    console.log('[ShareWharf]', message);
  }
});

console.log('ShareWharf 预加载脚本已加载');
