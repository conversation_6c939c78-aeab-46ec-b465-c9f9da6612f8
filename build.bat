@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    ShareWharf 桌面应用构建器
echo ========================================
echo.

echo 检查 Node.js 环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到 Node.js，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 检查依赖包...
if not exist "node_modules" (
    echo [警告] 未找到 node_modules 目录
    echo 正在安装依赖包...
    npm install
    if errorlevel 1 (
        echo [错误] 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 清理旧的构建文件...
if exist "dist" (
    rmdir /s /q "dist"
    echo 已清理 dist 目录
)

echo.
echo 开始构建 ShareWharf 桌面应用 (Windows 版本)...
echo 这可能需要几分钟时间，请耐心等待...
echo.

npm run build:win

if errorlevel 1 (
    echo.
    echo [错误] 构建失败
    echo 请检查错误信息并重试
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo    构建完成！
    echo ========================================
    echo.
    echo 构建文件位置: dist 目录
    echo 安装包已生成，可以分发给用户使用
    echo.
)

pause
