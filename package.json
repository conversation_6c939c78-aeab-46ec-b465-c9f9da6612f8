{"name": "sharewharf-desktop", "productName": "ShareWharf", "version": "1.0.0", "description": "ShareWharf 桌面应用", "main": "main.js", "homepage": "https://mall.sharewharf.com", "devDependencies": {"electron": "^37.2.4", "electron-builder": "^25.1.8", "nodemon": "^3.1.10"}, "scripts": {"start": "electron .", "dev": "nodemon --exec electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir"}, "build": {"appId": "com.sharewharf.mall.desktop", "productName": "ShareWharf", "directories": {"output": "dist"}, "files": ["main.js", "package.json", "assets/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "sign": false}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "category": "public.app-category.shopping"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "ShareWharf"}}, "keywords": ["electron", "desktop", "shopping", "mall"], "author": {"name": "hiram", "email": "<EMAIL>"}, "license": "ISC"}