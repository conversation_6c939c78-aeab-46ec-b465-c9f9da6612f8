<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- 内部装饰圆环 -->
  <circle cx="256" cy="256" r="200" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  <circle cx="256" cy="256" r="160" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
  
  <!-- 主要图标 - 购物袋形状 -->
  <g transform="translate(256,256)">
    <!-- 购物袋主体 -->
    <path d="M-60,-40 L60,-40 L70,80 L-70,80 Z" fill="rgba(255,255,255,0.95)" stroke="rgba(255,255,255,0.3)" stroke-width="2" rx="8"/>
    
    <!-- 购物袋手柄 -->
    <path d="M-40,-40 Q-40,-70 -20,-70 Q0,-70 0,-70 Q20,-70 40,-40" 
          fill="none" stroke="rgba(255,255,255,0.9)" stroke-width="6" stroke-linecap="round"/>
    
    <!-- 装饰波浪 -->
    <path d="M-50,-10 Q-25,0 0,-10 Q25,0 50,-10" 
          fill="none" stroke="url(#waveGradient)" stroke-width="4" stroke-linecap="round"/>
    <path d="M-50,10 Q-25,20 0,10 Q25,20 50,10" 
          fill="none" stroke="url(#waveGradient)" stroke-width="3" stroke-linecap="round"/>
    <path d="M-50,30 Q-25,40 0,30 Q25,40 50,30" 
          fill="none" stroke="url(#waveGradient)" stroke-width="2" stroke-linecap="round"/>
    
    <!-- 装饰点 -->
    <circle cx="-30" cy="50" r="3" fill="url(#waveGradient)"/>
    <circle cx="0" cy="55" r="4" fill="url(#waveGradient)"/>
    <circle cx="30" cy="50" r="3" fill="url(#waveGradient)"/>
    
    <!-- S 字母 -->
    <text x="-15" y="5" font-family="Arial, sans-serif" font-size="36" font-weight="bold" 
          fill="url(#waveGradient)" text-anchor="middle">S</text>
  </g>
  
  <!-- 外部光晕效果 -->
  <circle cx="256" cy="256" r="250" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
</svg>
