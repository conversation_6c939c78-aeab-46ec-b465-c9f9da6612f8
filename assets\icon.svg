<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#bgGradient)"/>

  <!-- 购物袋主体 -->
  <g transform="translate(128,128)">
    <path d="M-30,-20 L30,-20 L35,40 L-35,40 Z" fill="rgba(255,255,255,0.95)" rx="4"/>

    <!-- 购物袋手柄 -->
    <path d="M-20,-20 Q-20,-35 -10,-35 Q0,-35 0,-35 Q10,-35 20,-20"
          fill="none" stroke="rgba(255,255,255,0.9)" stroke-width="3" stroke-linecap="round"/>

    <!-- 装饰波浪 -->
    <path d="M-25,-5 Q-12.5,0 0,-5 Q12.5,0 25,-5"
          fill="none" stroke="url(#waveGradient)" stroke-width="2" stroke-linecap="round"/>
    <path d="M-25,5 Q-12.5,10 0,5 Q12.5,10 25,5"
          fill="none" stroke="url(#waveGradient)" stroke-width="2" stroke-linecap="round"/>

    <!-- S 字母 -->
    <text x="0" y="5" font-family="Arial, sans-serif" font-size="24" font-weight="bold"
          fill="url(#waveGradient)" text-anchor="middle">S</text>
  </g>
</svg>