# ShareWharf 购物商城桌面应用使用说明

## 快速开始

### 1. 开发运行
双击 `start.bat` 文件或在命令行中运行：
```bash
npm start
```

### 2. 构建安装包
双击 `build.bat` 文件或在命令行中运行：
```bash
npm run build:win
```

## 应用特性

### 🔒 安全性优化
- 禁用了 Node.js 集成，防止网页访问系统 API
- 启用了上下文隔离，确保主进程和渲染进程安全分离
- 禁用了远程模块，防止安全漏洞
- 启用了 Web 安全策略

### 🖥️ 窗口优化
- **默认尺寸**: 1400x900 像素，适合大多数屏幕
- **最小尺寸**: 1200x800 像素，确保内容完整显示
- **响应式**: 支持窗口缩放、最大化、全屏
- **居中显示**: 应用启动时自动居中

### 🌐 网络安全
- **域名限制**: 只允许在 sharewharf.com 域名内导航
- **外链处理**: 外部链接自动在默认浏览器中打开
- **错误处理**: 网络连接失败时显示友好提示

### 📋 菜单功能
- **文件菜单**: 刷新、强制刷新、退出
- **编辑菜单**: 撤销、重做、剪切、复制、粘贴、全选
- **视图菜单**: 重新加载、开发者工具、缩放控制、全屏
- **窗口菜单**: 最小化、关闭
- **帮助菜单**: 关于信息

### 🚀 性能优化
- **单实例运行**: 防止重复启动多个应用实例
- **现代用户代理**: 模拟最新 Chrome 浏览器，确保网站兼容性
- **内存优化**: 合理的窗口管理和资源释放

## 自定义配置

### 修改应用设置
编辑 `main.js` 文件中的 `APP_CONFIG` 对象：

```javascript
const APP_CONFIG = {
  url: 'https://mall.sharewharf.com/login',  // 目标网站
  title: 'ShareWharf 购物商城',              // 应用标题
  minWidth: 1200,                           // 最小宽度
  minHeight: 800,                           // 最小高度
  defaultWidth: 1400,                       // 默认宽度
  defaultHeight: 900                        // 默认高度
};
```

### 添加应用图标
将图标文件放置在 `assets` 目录下：
- **Windows**: `icon.ico` (推荐 256x256 或更高分辨率)
- **macOS**: `icon.icns` (推荐 512x512 或更高分辨率)  
- **Linux**: `icon.png` (推荐 512x512 或更高分辨率)

### 修改打包配置
编辑 `package.json` 中的 `build` 部分来自定义打包选项。

## 故障排除

### 应用无法启动
1. 确保已安装 Node.js (版本 16.0 或更高)
2. 运行 `npm install` 重新安装依赖
3. 检查是否有其他 Electron 应用正在运行

### 网站无法加载
1. 检查网络连接
2. 确认目标网站 https://mall.sharewharf.com 可正常访问
3. 尝试刷新页面 (Ctrl+R)

### 构建失败
1. 确保有足够的磁盘空间
2. 检查网络连接（构建过程需要下载依赖）
3. 尝试删除 `node_modules` 文件夹后重新运行 `npm install`

### 应用运行缓慢
1. 关闭不必要的其他应用程序
2. 检查系统内存使用情况
3. 尝试重启应用

## 开发者选项

### 启用开发者工具
在 `main.js` 中取消注释以下行：
```javascript
// mainWindow.webContents.openDevTools();
```

### 开发模式运行
```bash
npm run dev
```
这将启用热重载功能，代码修改后自动重启应用。

## 技术支持

如果遇到问题，请检查：
1. Node.js 版本是否符合要求
2. 网络连接是否正常
3. 系统防火墙设置
4. 杀毒软件是否误报

## 版本信息
- 应用版本: 1.0.0
- Electron 版本: 37.2.4
- 支持平台: Windows, macOS, Linux
