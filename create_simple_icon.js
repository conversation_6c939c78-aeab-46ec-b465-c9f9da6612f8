const fs = require('fs');
const path = require('path');

// 创建一个简单的 SVG 图标
const svgIcon = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#bgGradient)"/>

  <!-- 购物袋主体 -->
  <g transform="translate(128,128)">
    <path d="M-30,-20 L30,-20 L35,40 L-35,40 Z" fill="rgba(255,255,255,0.95)" rx="4"/>

    <!-- 购物袋手柄 -->
    <path d="M-20,-20 Q-20,-35 -10,-35 Q0,-35 0,-35 Q10,-35 20,-20"
          fill="none" stroke="rgba(255,255,255,0.9)" stroke-width="3" stroke-linecap="round"/>

    <!-- 装饰波浪 -->
    <path d="M-25,-5 Q-12.5,0 0,-5 Q12.5,0 25,-5"
          fill="none" stroke="url(#waveGradient)" stroke-width="2" stroke-linecap="round"/>
    <path d="M-25,5 Q-12.5,10 0,5 Q12.5,10 25,5"
          fill="none" stroke="url(#waveGradient)" stroke-width="2" stroke-linecap="round"/>

    <!-- S 字母 -->
    <text x="0" y="5" font-family="Arial, sans-serif" font-size="24" font-weight="bold"
          fill="url(#waveGradient)" text-anchor="middle">S</text>
  </g>
</svg>`;

// 保存 SVG 文件
fs.writeFileSync(path.join(__dirname, 'assets', 'icon.svg'), svgIcon);
console.log('SVG 图标已创建: assets/icon.svg');

// 创建一个简单的 ICO 文件（Windows）
// 这是一个最小的 ICO 文件格式
const createSimpleIco = () => {
  // 创建一个 32x32 的简单图标数据
  const iconData = Buffer.alloc(1078); // ICO 文件头 + 32x32 位图数据

  // ICO 文件头
  iconData.writeUInt16LE(0, 0);      // 保留字段
  iconData.writeUInt16LE(1, 2);      // 图标类型
  iconData.writeUInt16LE(1, 4);      // 图标数量

  // 图标目录条目
  iconData.writeUInt8(32, 6);        // 宽度
  iconData.writeUInt8(32, 7);        // 高度
  iconData.writeUInt8(0, 8);         // 颜色数
  iconData.writeUInt8(0, 9);         // 保留
  iconData.writeUInt16LE(1, 10);     // 颜色平面
  iconData.writeUInt16LE(32, 12);    // 位深度
  iconData.writeUInt32LE(1024, 14);  // 图像数据大小
  iconData.writeUInt32LE(22, 18);    // 图像数据偏移

  // 简单的位图数据（蓝色渐变）
  for (let i = 22; i < 1078; i += 4) {
    const y = Math.floor((i - 22) / 128);
    const intensity = Math.floor(255 * (1 - y / 32));
    iconData.writeUInt8(intensity, i);     // B
    iconData.writeUInt8(intensity, i + 1); // G
    iconData.writeUInt8(255, i + 2);       // R
    iconData.writeUInt8(255, i + 3);       // A
  }

  return iconData;
};

// 保存 ICO 文件
const icoData = createSimpleIco();
fs.writeFileSync(path.join(__dirname, 'assets', 'icon.ico'), icoData);
console.log('ICO 图标已创建: assets/icon.ico');

console.log('图标创建完成！');
console.log('请运行: node create_simple_icon.js');