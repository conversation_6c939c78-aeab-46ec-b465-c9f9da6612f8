const { app, BrowserWindow, Menu, shell, dialog, nativeImage, ipcMain } = require('electron');
const path = require('path');

// 应用配置
const APP_CONFIG = {
  url: 'https://mall.sharewharf.com/login',
  title: 'ShareWharf',
  minWidth: 1200,
  minHeight: 800,
  defaultWidth: 1400,
  defaultHeight: 900,
  // 图标路径
  iconPath: path.join(__dirname, 'assets', 'icon.png')
};

let mainWindow;

function createWindow() {
  // 尝试加载应用图标
  let appIcon = null;
  try {
    appIcon = nativeImage.createFromPath(APP_CONFIG.iconPath);
    if (appIcon.isEmpty()) {
      console.log('图标文件未找到，使用默认图标');
      appIcon = null;
    } else {
      console.log('成功加载应用图标');
    }
  } catch (error) {
    console.log('加载图标失败，使用默认图标:', error.message);
    appIcon = null;
  }

  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: APP_CONFIG.defaultWidth,
    height: APP_CONFIG.defaultHeight,
    minWidth: APP_CONFIG.minWidth,
    minHeight: APP_CONFIG.minHeight,
    title: APP_CONFIG.title,
    icon: appIcon, // 设置应用图标
    show: false, // 先不显示，等加载完成后再显示
    webPreferences: {
      nodeIntegration: false, // 安全性：禁用 Node.js 集成
      contextIsolation: true, // 安全性：启用上下文隔离
      enableRemoteModule: false, // 安全性：禁用远程模块
      webSecurity: true, // 启用 web 安全
      allowRunningInsecureContent: false, // 不允许运行不安全内容
      experimentalFeatures: false, // 禁用实验性功能
      preload: path.join(__dirname, 'preload.js') // 预加载脚本
    },
    autoHideMenuBar: false, // 显示菜单栏，提供更好的用户体验
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    titleBarOverlay: process.platform === 'win32' ? {
      color: '#667eea',
      symbolColor: '#ffffff',
      height: 32
    } : undefined,
    backgroundColor: '#f8f9fa', // 设置背景色为浅灰色
    center: true, // 窗口居中显示
    resizable: true, // 允许调整大小
    maximizable: true, // 允许最大化
    minimizable: true, // 允许最小化
    closable: true, // 允许关闭
    fullscreenable: true, // 允许全屏
    frame: true, // 显示窗口边框
    transparent: false, // 不透明窗口
    hasShadow: true, // 窗口阴影
    thickFrame: true, // 厚边框
    vibrancy: process.platform === 'darwin' ? 'under-window' : undefined, // macOS 毛玻璃效果
    roundedCorners: true, // 圆角窗口
    skipTaskbar: false, // 在任务栏显示
    alwaysOnTop: false, // 不置顶
    focusable: true, // 可获得焦点
    movable: true, // 可移动
    acceptFirstMouse: true // 接受第一次鼠标点击
  });

  // 设置用户代理，让网站识别为现代浏览器
  mainWindow.webContents.setUserAgent(
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  );

  // 加载网站
  mainWindow.loadURL(APP_CONFIG.url);

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // 可选：开发时打开开发者工具
    // mainWindow.webContents.openDevTools();
  });

  // 处理窗口关闭
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 处理新窗口打开（在默认浏览器中打开外部链接）
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // 处理导航事件（确保只在目标域名内导航）
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    const targetDomain = 'sharewharf.com';

    if (!parsedUrl.hostname.includes(targetDomain)) {
      event.preventDefault();
      shell.openExternal(navigationUrl);
    }
  });

  // 处理页面加载错误
  mainWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription) => {
    if (errorCode !== -3) { // -3 是用户取消加载，不需要处理
      dialog.showErrorBox(
        '加载失败',
        `无法加载页面: ${errorDescription}\n\n请检查网络连接后重试。`
      );
    }
  });

  // 创建应用菜单
  createMenu();
}

function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '刷新',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            if (mainWindow) {
              mainWindow.reload();
            }
          }
        },
        {
          label: '强制刷新',
          accelerator: 'CmdOrCtrl+Shift+R',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.reloadIgnoringCache();
            }
          }
        },
        { type: 'separator' },
        {
          label: '主页',
          accelerator: 'CmdOrCtrl+H',
          click: () => {
            if (mainWindow) {
              console.log('返回主页');
              mainWindow.loadURL(APP_CONFIG.url);
            }
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' },
        { label: '全选', accelerator: 'CmdOrCtrl+A', role: 'selectall' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: '强制重新加载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: '切换开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: '切换全屏', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: '窗口',
      submenu: [
        { label: '最小化', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: '关闭', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于 ShareWharf',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于 ShareWharf',
              message: 'ShareWharf 桌面应用',
              detail: `版本: 1.0.0
基于 Electron 构建的现代桌面应用

特性:
• 安全的网页浏览体验
• 优化的桌面界面
• 快捷键支持
• 跨平台兼容

开发者: hiram
构建时间: ${new Date().getFullYear()}年`,
              buttons: ['确定'],
              defaultId: 0,
              icon: nativeImage.createFromPath(APP_CONFIG.iconPath)
            });
          }
        },
        { type: 'separator' },
        {
          label: '检查更新',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '检查更新',
              message: '当前已是最新版本',
              detail: '版本 1.0.0 是最新的稳定版本。',
              buttons: ['确定'],
              defaultId: 0
            });
          }
        },
        {
          label: '访问官网',
          click: () => {
            shell.openExternal('https://mall.sharewharf.com');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 应用准备就绪时创建窗口
app.whenReady().then(() => {
  createWindow();
});

// 所有窗口关闭时退出应用（macOS 除外）
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// macOS 上点击 dock 图标时重新创建窗口
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 防止应用被多次启动
app.on('second-instance', () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

// 设置应用为单实例
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
}