<!DOCTYPE html>
<html>
<head>
    <title>ShareWharf Icon Generator</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .container { text-align: center; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ShareWharf 图标生成器</h1>
        <canvas id="iconCanvas" width="512" height="512"></canvas>
        <br>
        <button onclick="generateIcon()">生成图标</button>
        <button onclick="downloadIcon()">下载图标</button>
        <p>右键点击图标可以保存为 PNG 文件</p>
    </div>

    <script>
        function generateIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            const size = 512;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 20, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            
            // 添加阴影效果
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 20;
            ctx.shadowOffsetY = 10;
            
            // 绘制内部装饰圆环
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 60, 0, 2 * Math.PI);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 重置阴影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetY = 0;
            
            // 绘制购物袋主体
            const centerX = size / 2;
            const centerY = size / 2;
            const bagWidth = 120;
            const bagHeight = 140;
            
            ctx.beginPath();
            ctx.moveTo(centerX - bagWidth/2 + 10, centerY - 40);
            ctx.lineTo(centerX + bagWidth/2 - 10, centerY - 40);
            ctx.lineTo(centerX + bagWidth/2, centerY + bagHeight - 40);
            ctx.lineTo(centerX - bagWidth/2, centerY + bagHeight - 40);
            ctx.closePath();
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.fill();
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制购物袋手柄
            ctx.beginPath();
            ctx.arc(centerX - 30, centerY - 40, 25, Math.PI, 0, false);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.lineWidth = 8;
            ctx.lineCap = 'round';
            ctx.stroke();
            
            ctx.beginPath();
            ctx.arc(centerX + 30, centerY - 40, 25, Math.PI, 0, false);
            ctx.stroke();
            
            // 绘制装饰波浪
            const waveGradient = ctx.createLinearGradient(centerX - 50, 0, centerX + 50, 0);
            waveGradient.addColorStop(0, '#4facfe');
            waveGradient.addColorStop(1, '#00f2fe');
            
            // 第一条波浪
            ctx.beginPath();
            ctx.moveTo(centerX - 50, centerY - 10);
            ctx.quadraticCurveTo(centerX - 25, centerY, centerX, centerY - 10);
            ctx.quadraticCurveTo(centerX + 25, centerY, centerX + 50, centerY - 10);
            ctx.strokeStyle = waveGradient;
            ctx.lineWidth = 4;
            ctx.stroke();
            
            // 第二条波浪
            ctx.beginPath();
            ctx.moveTo(centerX - 50, centerY + 10);
            ctx.quadraticCurveTo(centerX - 25, centerY + 20, centerX, centerY + 10);
            ctx.quadraticCurveTo(centerX + 25, centerY + 20, centerX + 50, centerY + 10);
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 第三条波浪
            ctx.beginPath();
            ctx.moveTo(centerX - 50, centerY + 30);
            ctx.quadraticCurveTo(centerX - 25, centerY + 40, centerX, centerY + 30);
            ctx.quadraticCurveTo(centerX + 25, centerY + 40, centerX + 50, centerY + 30);
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制装饰点
            ctx.fillStyle = waveGradient;
            ctx.beginPath();
            ctx.arc(centerX - 30, centerY + 50, 3, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(centerX, centerY + 55, 4, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(centerX + 30, centerY + 50, 3, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制 "S" 字母
            ctx.font = 'bold 48px Arial';
            ctx.fillStyle = waveGradient;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 添加文字阴影
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            ctx.fillText('S', centerX, centerY + 5);
            
            // 重置阴影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // 外部光晕
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 10, 0, 2 * Math.PI);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            ctx.stroke();
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'sharewharf-icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 页面加载时自动生成图标
        window.onload = function() {
            generateIcon();
        };
    </script>
</body>
</html>
