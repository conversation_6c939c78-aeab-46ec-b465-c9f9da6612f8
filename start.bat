@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    ShareWharf 桌面应用启动器
echo ========================================
echo.

echo 检查 Node.js 环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到 Node.js，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 检查依赖包...
if not exist "node_modules" (
    echo [警告] 未找到 node_modules 目录
    echo 正在安装依赖包...
    npm install
    if errorlevel 1 (
        echo [错误] 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 启动 ShareWharf 桌面应用...
echo.
npm start

if errorlevel 1 (
    echo.
    echo [错误] 应用启动失败
    echo 请检查错误信息并重试
)

echo.
echo 应用已关闭
pause
