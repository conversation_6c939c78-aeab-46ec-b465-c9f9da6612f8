@echo off
chcp 65001 >nul
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ShareWharf 桌面应用                       ║
echo ║                      优化完成演示                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎉 恭喜！ShareWharf 桌面应用已经全面优化完成！
echo.
echo ✨ 主要优化成果：
echo    • 应用名称简化为 "ShareWharf"
echo    • 精美的渐变色图标设计
echo    • 优化的窗口标题栏和界面
echo    • 增强的用户体验和快捷键
echo    • 完善的安全性和跨平台支持
echo.
echo 🚀 可用操作：
echo    [1] 启动应用程序
echo    [2] 构建安装包
echo    [3] 查看项目文件
echo    [4] 打开图标生成器
echo    [5] 退出
echo.
set /p choice=请选择操作 (1-5): 

if "%choice%"=="1" (
    echo.
    echo 🚀 正在启动 ShareWharf 桌面应用...
    start.bat
) else if "%choice%"=="2" (
    echo.
    echo 📦 正在构建安装包...
    build.bat
) else if "%choice%"=="3" (
    echo.
    echo 📁 项目文件结构：
    echo.
    dir /b
    echo.
    pause
) else if "%choice%"=="4" (
    echo.
    echo 🎨 正在打开图标生成器...
    start assets\create_icon.html
) else if "%choice%"=="5" (
    echo.
    echo 👋 感谢使用 ShareWharf 桌面应用！
    timeout /t 2 >nul
    exit
) else (
    echo.
    echo ❌ 无效选择，请重新运行脚本
    pause
)
