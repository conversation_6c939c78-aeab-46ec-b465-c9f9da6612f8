# ShareWharf 桌面应用

🛍️ 一个现代化的购物商城桌面应用程序，基于 Electron 构建，为用户提供优质的桌面购物体验。

![ShareWharf Logo](assets/icon.png)

## ✨ 特性

- 🔒 **安全可靠** - 采用最新的 Electron 安全最佳实践
- 🎨 **精美界面** - 优化的桌面界面设计，支持自定义标题栏
- ⚡ **性能优化** - 快速启动，流畅操作体验
- 🌐 **跨平台** - 支持 Windows、macOS 和 Linux
- 🔧 **易于使用** - 简单的安装和使用流程
- 📱 **响应式设计** - 适配不同屏幕尺寸
- ⌨️ **快捷键支持** - 丰富的键盘快捷键操作

## 功能特性

- 🖥️ 原生桌面应用体验
- 🔒 增强的安全性配置
- 📱 响应式界面，适配不同屏幕尺寸
- 🎯 优化的窗口管理
- 🔄 自动刷新和错误处理
- 📋 完整的菜单栏功能
- 🚀 单实例运行防止重复启动

## 开发环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

## 安装依赖

```bash
npm install
```

## 开发运行

```bash
# 开发模式（带热重载）
npm run dev

# 普通启动
npm start
```

## 构建打包

### 安装打包工具

```bash
npm install electron-builder --save-dev
```

### 构建不同平台的应用

```bash
# 构建 Windows 版本
npm run build:win

# 构建 macOS 版本
npm run build:mac

# 构建 Linux 版本
npm run build:linux

# 构建所有平台
npm run build
```

## 应用配置

### 窗口设置
- 默认尺寸：1400x900
- 最小尺寸：1200x800
- 支持全屏、最大化、最小化
- 窗口居中显示

### 安全配置
- 禁用 Node.js 集成
- 启用上下文隔离
- 禁用远程模块
- 启用 Web 安全

### 菜单功能
- 文件：刷新、强制刷新、退出
- 编辑：撤销、重做、剪切、复制、粘贴、全选
- 视图：重新加载、开发者工具、缩放控制、全屏
- 窗口：最小化、关闭
- 帮助：关于

## 图标配置

将应用图标放置在 `assets` 目录下：
- Windows: `icon.ico` (256x256 或更高)
- macOS: `icon.icns` (512x512 或更高)
- Linux: `icon.png` (512x512 或更高)

## 自定义配置

可以在 `main.js` 中的 `APP_CONFIG` 对象中修改：
- 应用标题
- 默认窗口尺寸
- 目标网站 URL
- 最小窗口尺寸

## 网络安全

- 只允许在 sharewharf.com 域名内导航
- 外部链接自动在默认浏览器中打开
- 防止恶意网站跳转

## 故障排除

### 网络连接问题
如果无法加载网站，请检查：
1. 网络连接是否正常
2. 目标网站是否可访问
3. 防火墙设置

### 构建问题
如果构建失败，请尝试：
1. 清除 node_modules 并重新安装
2. 检查 Node.js 版本是否符合要求
3. 确保有足够的磁盘空间

## 许可证

ISC License

## 作者

hiram
